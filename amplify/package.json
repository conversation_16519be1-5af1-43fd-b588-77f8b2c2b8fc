{"name": "monova-digital-wardrobe-lambda", "version": "1.0.0", "description": "Lambda functions for Monova Digital Wardrobe", "main": "index.js", "type": "module", "scripts": {"build": "tsc"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.751.0", "@aws-sdk/lib-dynamodb": "^3.751.0", "@azure/storage-blob": "^12.27.0", "@elastic/elasticsearch": "^8.17.1", "@google/generative-ai": "^0.22.0", "@opensearch-project/opensearch": "^3.3.0", "axios": "^1.7.9", "image-collage-maker": "^1.0.3", "node-fetch": "^3.3.2", "typechat": "^0.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^18.x", "@types/uuid": "^9.0.0", "typescript": "^5.4.5"}}