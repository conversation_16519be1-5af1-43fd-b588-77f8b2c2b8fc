import { defineBackend } from "@aws-amplify/backend";
import { auth } from "./auth/resource";
import { search } from "./functions/src/functions/search/resource";
import { apparelController } from "./functions/src/functions/apparel/resource";
import { outfitController } from "./functions/src/functions/outfit/resource";
import { styleProfileController } from "./functions/src/functions/style/resource";
import { inventoryController } from "./functions/src/functions/inventory/resource";
import { feedbackController } from "./functions/src/functions/feedback/resource";
import { manychatController } from "./functions/src/functions/manychat/resource";

import { Stack } from "aws-cdk-lib";
import {
  AuthorizationType,
  Cors,
  LambdaIntegration,
  RestApi,
} from "aws-cdk-lib/aws-apigateway";
import { Effect, Policy, PolicyStatement } from "aws-cdk-lib/aws-iam";

// Request template for Lambda integrations with proxy=false
// Simple template with just the essential elements needed
const LAMBDA_REQUEST_TEMPLATE = `{
  "path": "$context.resourcePath",
  "httpMethod": "$context.httpMethod",
  "body": "$util.escapeJavaScript($input.body)"
}`;

const backend = defineBackend({
  auth,
  search,
  apparelController,
  inventoryController,
  outfitController,
  styleProfileController,
  feedbackController,
  manychatController,
});
const apiStack = backend.createStack("monova-wardrobe-api-stack");

//---------------------------------<Apparel API>---------------------------------//
const apparelApi = new RestApi(apiStack, "ApparelApi_DEV", {
  restApiName: "apparelApi_DEV",
  description: "API for managing apparel items",
  deploy: true,
  deployOptions: {
    stageName: "dev",
    tracingEnabled: true,
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Create Lambda integration
const apparelLambdaIntegration = new LambdaIntegration(
  backend.apparelController.resources.lambda
);

// Add API resources and methods
const apparelDefualtPath = apparelApi.root.addResource("apparel", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

apparelDefualtPath.addMethod("GET", apparelLambdaIntegration);
apparelDefualtPath.addMethod("POST", apparelLambdaIntegration);
apparelDefualtPath.addMethod("PUT", apparelLambdaIntegration);
apparelDefualtPath.addMethod("DELETE", apparelLambdaIntegration);

apparelDefualtPath.addProxy({
  anyMethod: true,
  defaultIntegration: apparelLambdaIntegration,
});

//---------------------------------<outfit API>---------------------------------//
const outfitApi = new RestApi(apiStack, "OutfitApi_DEV", {
  restApiName: "outfitApi_DEV",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Create Lambda integration for search controller
const outfitLambdaIntegration = new LambdaIntegration(
  backend.outfitController.resources.lambda
);

// Add search resource path
const outfitDefaultPath = outfitApi.root.addResource("outfit", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

// Add methods to search path
outfitDefaultPath.addMethod("GET", outfitLambdaIntegration);
outfitDefaultPath.addMethod("POST", outfitLambdaIntegration);
outfitDefaultPath.addMethod("DELETE", outfitLambdaIntegration);
outfitDefaultPath.addMethod("PUT", outfitLambdaIntegration);

// Add proxy to handle all other methods
outfitDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: outfitLambdaIntegration,
});

//---------------------------------<Search API>---------------------------------//

// Create Search API
const searchApi = new RestApi(apiStack, "SearchApi_DEV", {
  restApiName: "searchApi_DEV",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Create Lambda integration for search controller
const searchLambdaIntegration = new LambdaIntegration(
  backend.search.resources.lambda
);

// Add search resource path
const searchDefaultPath = searchApi.root.addResource("search", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

// Add methods to search path
searchDefaultPath.addMethod("GET", searchLambdaIntegration);
searchDefaultPath.addMethod("POST", searchLambdaIntegration);
searchDefaultPath.addMethod("DELETE", searchLambdaIntegration);
searchDefaultPath.addMethod("PUT", searchLambdaIntegration);

// Add proxy to handle all other methods
searchDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: searchLambdaIntegration,
});
//---------------------------------<user Style Profile API>---------------------------------//
const styleProfileApi = new RestApi(apiStack, "StyleProfileApi_DEV", {
  restApiName: "styleProfileApi_DEV",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Create Lambda integration
const styleProfileLambdaIntegration = new LambdaIntegration(
  backend.styleProfileController.resources.lambda
);

// Add API resources and methods
const styleProfileDefaultPath = styleProfileApi.root.addResource(
  "style-profile",
  {
    defaultMethodOptions: {
      authorizationType: AuthorizationType.NONE,
    },
  }
);

styleProfileDefaultPath.addMethod("GET", styleProfileLambdaIntegration);
styleProfileDefaultPath.addMethod("POST", styleProfileLambdaIntegration);
styleProfileDefaultPath.addMethod("PUT", styleProfileLambdaIntegration);
styleProfileDefaultPath.addMethod("DELETE", styleProfileLambdaIntegration);

styleProfileDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: styleProfileLambdaIntegration,
});

//---------------------------------<Feedback API>---------------------------------//
// Create the Feedback API
const feedbackApi = new RestApi(apiStack, "FeedbackApi_DEV", {
  restApiName: "feedbackApi_DEV",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Lambda integration with our feedback controller
const feedbackLambdaIntegration = new LambdaIntegration(
  backend.feedbackController.resources.lambda
);

// API Resources configuration
const feedbackResource = feedbackApi.root.addResource("feedback", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

feedbackResource.addMethod("POST", feedbackLambdaIntegration);
feedbackResource.addMethod("GET", feedbackLambdaIntegration);

// Add proxy to handle all other methods
feedbackResource.addProxy({
  anyMethod: true,
  defaultIntegration: feedbackLambdaIntegration,
});

//---------------------------------<ManyChat API>---------------------------------//
// Create the ManyChat API
const manychatApi = new RestApi(apiStack, "ManyChatApi_DEV", {
  restApiName: "manychatApi_DEV",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Lambda integration with our manychat controller
const manychatLambdaIntegration = new LambdaIntegration(
  backend.manychatController.resources.lambda
);

// Lambda integration for async with our manychat controller
const asyncManychatLambdaIntegration = new LambdaIntegration(
  backend.manychatController.resources.lambda, {
    proxy: false, // Required to customize headers
    requestParameters: {
      'integration.request.header.X-Amz-Invocation-Type': "'Event'"
    },
    // Use the request template constant to ensure Lambda receives the full event format
    requestTemplates: {
      'application/json': LAMBDA_REQUEST_TEMPLATE
    },
    integrationResponses: [
      {
        statusCode: "200",
        selectionPattern: ".*",
        responseTemplates: {
          "application/json": JSON.stringify({ message: "Request accepted" })
        }
      }
    ]
  }
);

// API Resources configuration
const manychatResource = manychatApi.root.addResource("manychat", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});
manychatResource.addMethod("POST", manychatLambdaIntegration);
manychatResource.addMethod("GET", manychatLambdaIntegration);
manychatResource.addProxy({
  anyMethod: true,
  defaultIntegration: manychatLambdaIntegration,
});
const manychatApparelResource = manychatResource.addResource("apparels")
const manychatCreateApaprelResource = manychatApparelResource.addResource("create", {
  defaultMethodOptions : {
    authorizationType: AuthorizationType.NONE,
  }
})
manychatCreateApaprelResource.addMethod("POST", asyncManychatLambdaIntegration, {
  methodResponses: [{ statusCode: "200" }]
});
manychatApparelResource.addProxy({
  anyMethod: true,
  defaultIntegration: manychatLambdaIntegration,
});

// Add outfits resource for Manychat
const manychatOutfitsResource = manychatResource.addResource("outfits");

// Add setExistingItemsOutfit resource with async integration
const manychatSetExistingItemsOutfitResource = manychatOutfitsResource.addResource("setExistingItemsOutfit", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  }
});

manychatSetExistingItemsOutfitResource.addMethod("POST", asyncManychatLambdaIntegration, {
  methodResponses: [{ statusCode: "200" }]
});

// Add reviewMyOutfit resource with sync integration
const manychatReviewMyOutfitResource = manychatOutfitsResource.addResource("reviewMyOutfit", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  }
});

manychatReviewMyOutfitResource.addMethod("POST", manychatLambdaIntegration);

manychatOutfitsResource.addProxy({
  anyMethod: true,
  defaultIntegration: manychatLambdaIntegration,
});

// Add util resource for Manychat
const manychatUtilResource = manychatResource.addResource("util", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

// Add proxy to handle all util sub-paths
manychatUtilResource.addProxy({
  anyMethod: true,
  defaultIntegration: manychatLambdaIntegration,
});

//---------------------------------<Ddb Policy statement>---------------------------------//

const actions: string[] = [
  "dynamodb:PutItem",
  "dynamodb:GetItem",
  "dynamodb:Query",
  "dynamodb:UpdateItem",
  "dynamodb:DeleteItem",
  "dynamodb:BatchGetItem",
  "dynamodb:BatchWriteItem",
  "dynamodb:Scan",
];
// Policy for Apparel Controller Lambda
const apparelDdbPolicyStatement = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: actions,
  resources: [
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/apparel_store_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/apparel_store_monova_dev/index/*`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_wardrobe_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_wardrobe_monova_dev/index/*`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/outfit_collection_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/outfit_collection_monova_dev/index/*`,
  ],
});

// Policy for Outfit Controller Lambda
const outfitDdbPolicyStatement = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: actions,
  resources: [
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/outfit_store_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/outfit_store_monova_dev/index/*`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/outfit_collection_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/outfit_collection_monova_dev/index/*`,
  ],
});

// Policy for User Style Profile Controller Lambda
const userStyleProfileDdbPolicyStatement = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: actions,
  resources: [
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_style_profile_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_style_profile_dev/index/*`,
  ],
});

const feedbackDdbPolicyStatement = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: actions,
  resources: [
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_feedback_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_feedback_monova_dev/index/*`,
  ],
});

// Policy for Manychat Controller Lambda to access required tables
const manychatDdbPolicyStatement = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: actions,
  resources: [
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_wardrobe_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_wardrobe_monova_dev/index/*`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/apparel_store_monova_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/apparel_store_monova_dev/index/*`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_style_profile_dev`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/user_style_profile_dev/index/*`,
  ],
});

// Adding apparel edit permissions to apparel controller
backend.apparelController.resources.lambda.addToRolePolicy(
  apparelDdbPolicyStatement
);

// Adding all permissions to outfit controller
backend.outfitController.resources.lambda.addToRolePolicy(
  outfitDdbPolicyStatement
);
backend.outfitController.resources.lambda.addToRolePolicy(
  apparelDdbPolicyStatement
);
backend.outfitController.resources.lambda.addToRolePolicy(
  userStyleProfileDdbPolicyStatement
);

// Adding style profile permissions to style profile controller
backend.styleProfileController.resources.lambda.addToRolePolicy(
  userStyleProfileDdbPolicyStatement
);

// Adding feedback permissions to feedback controller
backend.feedbackController.resources.lambda.addToRolePolicy(
  feedbackDdbPolicyStatement
);

// Adding DynamoDB permissions to Manychat controller
backend.manychatController.resources.lambda.addToRolePolicy(
  manychatDdbPolicyStatement
);

//---------------------------------<Apparel Collection (open search) API>---------------------------------//
// Create the API Gateway REST API
const apparelCollectionApi = new RestApi(apiStack, "ApparelCollectionApi", {
  restApiName: "apparelCollectionApi_DEV",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  // CORS configuration kept as-is since it's essential for web clients
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS,
    allowMethods: Cors.ALL_METHODS,
    allowHeaders: Cors.DEFAULT_HEADERS,
  },
});

// Lambda integration with our apparel controller
const apparelCollectionLambdaIntegration = new LambdaIntegration(
  backend.inventoryController.resources.lambda
);

// OpenSearch specific permissions
// KEPT: Only essential OpenSearch permissions, removed unnecessary ones
const openSearchPolicy = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: [
    // Essential OpenSearch operations for our use case
    "aoss:APIAccessAll", // Required for general OpenSearch access
    "aoss:BatchGetCollection", // For batch operations
    "aoss:CreateIndex", // For initial index creation
    "aoss:DescribeIndex", // For index operations
    "aoss:ReadDocument", // For search operations
    "aoss:WriteDocument", // For create/update operations
  ],
  resources: ["*"],
});

// Add OpenSearch permissions to Lambda
backend.inventoryController.resources.lambda.addToRolePolicy(
  openSearchPolicy
);

// API Resources configuration
const apparelCollectionResource = apparelCollectionApi.root.addResource(
  "apparels",
  {
    defaultMethodOptions: {
      authorizationType: AuthorizationType.NONE, // Kept as no auth for simplicity
    },
  }
);

apparelCollectionResource.addMethod("POST", apparelCollectionLambdaIntegration);
apparelCollectionResource.addMethod("GET", apparelCollectionLambdaIntegration);

// Add proxy to handle all other methods
apparelCollectionResource.addProxy({
  anyMethod: true,
  defaultIntegration: apparelCollectionLambdaIntegration,
});

// Create Collection API policy
const collectionApiPolicy = new Policy(apiStack, "ApparelCollectionApiPolicy", {
  statements: [
    new PolicyStatement({
      effect: Effect.ALLOW,
      actions: [
        // API Gateway permissions
        "execute-api:Invoke",
        "execute-api:ManageConnections",
        // OpenSearch permissions (same as above)
        "aoss:APIAccessAll",
        "aoss:BatchGetCollection",
        "aoss:CreateIndex",
        "aoss:DescribeIndex",
        "aoss:ReadDocument",
        "aoss:WriteDocument",
      ],
      resources: [
        // Only include resources we actually use
        `${apparelCollectionApi.arnForExecuteApi("*", "/", "dev")}`,
        `${apparelCollectionApi.arnForExecuteApi("*", "/apparels", "dev")}`,
        `${apparelCollectionApi.arnForExecuteApi(
          "*",
          "/apparels/search",
          "dev"
        )}`,
      ],
    }),
  ],
});

// SIMPLIFIED: Only attach policy to required roles
backend.inventoryController.resources.lambda.role?.attachInlinePolicy(
  collectionApiPolicy
);
backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(
  collectionApiPolicy
);
//This has to be added once we add the manychat controller to the backend
//---------------------------------<API Rest Policy>---------------------------------//
const apiRestPolicy = new Policy(apiStack, "APIRestPolicy", {
  statements: [
    new PolicyStatement({
      actions: [
        "execute-api:Invoke",
        "execute-api:ManageConnections",
        "dynamodb:PutItem",
        "dynamodb:GetItem",
        "dynamodb:Query",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem",
        "dynamodb:BatchGetItem",
        "dynamodb:BatchWriteItem",
      ],
      resources: [
        `${apparelApi.arnForExecuteApi("*", "/", "dev")}`,
        `${apparelApi.arnForExecuteApi("*", "/search/*", "dev")}`,
        `${searchApi.arnForExecuteApi("*", "/", "dev")}`,
        `${searchApi.arnForExecuteApi("*", "/apparel/*", "dev")}`,
        `${outfitApi.arnForExecuteApi("*", "/", "dev")}`,
        `${outfitApi.arnForExecuteApi("*", "/outfit/*", "dev")}`,
        `${styleProfileApi.arnForExecuteApi("*", "/", "dev")}`,
        `${styleProfileApi.arnForExecuteApi("*", "/style-profile/*", "dev")}`,
        `${feedbackApi.arnForExecuteApi("*", "/", "dev")}`,
        `${feedbackApi.arnForExecuteApi("*", "/feedback/*", "dev")}`,
        `${manychatApi.arnForExecuteApi("*", "/", "dev")}`,
        `${manychatApi.arnForExecuteApi("*", "/manychat/*", "dev")}`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/user_wardrobe_monova_dev`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/user_wardrobe_monova_dev/index/*`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/apparel_store_monova_dev`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/apparel_store_monova_dev/index/*`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/user_style_profile_dev`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/user_style_profile_dev/index/*`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/user_feedback_monova_dev`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/user_feedback_monova_dev/index/*`,
      ],
    }),
  ],
});

backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(
  apiRestPolicy
);
backend.auth.resources.unauthenticatedUserIamRole.attachInlinePolicy(
  apiRestPolicy
);

// Output API configuration
backend.addOutput({
  custom: {
    API: {
      [searchApi.restApiName]: {
        endpoint: searchApi.url,
        region: Stack.of(searchApi).region,
        apiName: searchApi.restApiName,
      },
      [apparelApi.restApiName]: {
        endpoint: apparelApi.url,
        region: Stack.of(apparelApi).region,
        apiName: apparelApi.restApiName,
      },
      [apparelCollectionApi.restApiName]: {
        endpoint: apparelCollectionApi.url,
        region: Stack.of(apparelCollectionApi).region,
        apiName: apparelCollectionApi.restApiName,
      },
      [outfitApi.restApiName]: {
        endpoint: outfitApi.url,
        region: Stack.of(outfitApi).region,
        apiName: outfitApi.restApiName,
      },
      [styleProfileApi.restApiName]: {
        endpoint: styleProfileApi.url,
        region: Stack.of(styleProfileApi).region,
        apiName: styleProfileApi.restApiName,
      },
      [feedbackApi.restApiName]: {
        endpoint: feedbackApi.url,
        region: Stack.of(feedbackApi).region,
        apiName: feedbackApi.restApiName,
      },
      [manychatApi.restApiName]: {
        endpoint: manychatApi.url,
        region: Stack.of(manychatApi).region,
        apiName: manychatApi.restApiName,
      },
    },
  },
});
