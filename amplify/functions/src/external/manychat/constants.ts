/**
 * Manychat API endpoints
 */
export const MANYCHAT_ENDPOINTS = {
  SET_CUSTOM_FIELD: '/subscriber/setCustomField',
  SET_CUSTOM_FIELDS: '/subscriber/setCustomFields',
};

/**
 * Status codes for Manychat operations
 * Used to indicate the status of various operations in Manychat fields
 */
export enum MANYCHAT_STATUS {
  SUCCESS = "SUCCESS",
  FAILURE = "FAILURE",
  SUCCESS_WITH_DUPLICATES = "SUCCESS_WITH_DUPLICATES",
  TOTAL_FAILURE = "TOTAL_FAILURE",
  IN_PROGRESS = "IN_PROGRESS"
}

/**
 * Boolean values for Manychat fields
 * Used for fields that require string representation of boolean values
 */
export enum MANYCHAT_BOOLEAN {
  TRUE = "TRUE",
  FALSE = "FALSE"
}

/**
 * Placeholder values used by Manychat
 * These values indicate that a field doesn't have a real value
 * and should be treated as empty/undefined
 */
export const MANYCHAT_PLACEHOLDERS = {
  NA: "NA",
  DOUBLE_BRACES_PREFIX: "{{"
};

/**
 * Field IDs for Manychat custom fields
 * In Manychat, at contact level, we have defined these custom user fields.
 * As part of the operation, we are setting the values for these fields.
 */
export const MANYCHAT_FIELD_IDS = {
  // Item 1 fields
  ITEM1_CATEGORY: 12760582,
  ITEM1_IMAGE_URL: 12760576,
  ITEM1_PRODUCT_NAME: 12760575,
  ITEM1_PRODUCT_URL: 12760578,

  // Item 2 fields
  ITEM2_CATEGORY: 12760594,
  ITEM2_IMAGE_URL: 12760592,
  ITEM2_PRODUCT_NAME: 12760591,
  ITEM2_PRODUCT_URL: 12760585,

  // Item 3 fields
  ITEM3_CATEGORY: 12760595,
  ITEM3_IMAGE_URL: 12760597,
  ITEM3_PRODUCT_NAME: 12760596,
  ITEM3_PRODUCT_URL: 12760598,

  // Item 4 fields
  ITEM4_CATEGORY: 12760607,
  ITEM4_IMAGE_URL: 12760606,
  ITEM4_PRODUCT_NAME: 12760651,
  ITEM4_PRODUCT_URL: 12760599,

  // Number of items field
  NUMBER_OF_ITEMS: 12760574,

  // Outfit fields
  OUTFIT_COLLAGE_URL: 12804722,
  OUTFIT_NAME: 12939649,

  //Outfit Generation Fields
  TEMP_GPT_RESPONSE: 11331064,
  PLAIN_GPT_RESPONSE: 11818819,

  // Apparel statistics fields
  TOTAL_TOPWEAR_COUNT: 12922838,
  TOTAL_ITEM_COUNT: 12922837,
  TOTAL_FOOTWEAR_COUNT: 12922840,
  TOTAL_BOTTOMWEAR_COUNT: 12922839,

  // Apparel addition status fields
  APPAREL_ADDITION_STATUS: 12922844,
  APPAREL_ADDITION_ERROR_MESSAGE: 12922848,
  APPAREL_ADDITION_DUPLICATE_ITEMS: 12922847,
  APPAREL_ADDITION_COLLAGE_TEMP: 12922845,

  // Apparel category fields for Manychat
  CURRENT_TOPWEAR_COLLAGE_URL: 12923394,
  CURRENT_TOPWEAR_DETAILS: 12923399,
  CURRENT_BOTTOMWEAR_COLLAGE_URL: 12923395,
  CURRENT_BOTTOMWEAR_DETAILS: 12923402,
  CURRENT_FOOTWEAR_COLLAGE_URL: 12923396,
  CURRENT_FOOTWEAR_DETAILS: 12923401,

  // Outfit curation status fields
  OUTFIT_CURATION_ERROR_MESSAGE: 12939771,
  OUTFIT_CURATION_STATUS: 12939772,

  // Style profile fields
  STYLE_PROFILE_EXISTS: 12940014,

  // User location field
  USER_LOCATION: 12991629
};
