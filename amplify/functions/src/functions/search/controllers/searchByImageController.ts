import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { SearchService } from "../../../services/searchService";
import { SearchError } from "../utils/errors";
import { authenticate } from "../middleware/auth";

const searchService = new SearchService();

export const searchByImageController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    // Authenticate the user
   // await authenticate(event);

    // Parse the request body
    const body = JSON.parse(event.body || "{}");
    const { imageUrl, gender } = body;

    // Validate required parameters
    if (!imageUrl || !gender) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "Missing required parameters: imageUrl and gender",
        }),
      };
    }

    // Perform the search
    const searchResults = await searchService.searchByImage(imageUrl, gender);

    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        message: "Search completed successfully",
        results: searchResults,
      }),
    };
  } catch (error) {
    console.error("Error in search controller:", error);
    const statusCode = error instanceof SearchError ? error.statusCode : 500;
    return {
      statusCode,
      body: JSON.stringify({
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      }),
    };
  }
}; 