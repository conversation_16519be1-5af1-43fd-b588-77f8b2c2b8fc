import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { handleRoutes } from "./routes";
import { ManyChatError, formatResponse } from "./utils/errors";

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    return await handleRoutes(event);
  } catch (error) {
    console.error("Error:", error);
    
    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        { message: error.message },
        error.errorCode
      );
    }

    return formatResponse(
      500,
      { message: "Internal server error" },
      "INTERNAL_SERVER_ERROR"
    );
  }
}; 