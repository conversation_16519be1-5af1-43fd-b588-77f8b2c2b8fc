import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { manychatOutfitService } from "../../../../services/manychat";
import { ManychatReviewMyOutfitRequest } from "../../../../types/manychat";
import { isValidUrl } from "../../../../utils/urlUtil";

export const reviewMyOutfit = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return formatResponse(400, {
        status: "FAILURE",
        message: "Request body is required",
        timestamp: new Date().toISOString(),
      });
    }

    const requestBody = JSON.parse(event.body);
    const { userId, manychatSubscriberId, imageURL, imageCaption } = requestBody;

    // Validate required parameters
    if (!userId) {
      return formatResponse(400, {
        status: "FAILURE",
        message: "userId is required",
        timestamp: new Date().toISOString(),
      });
    }

    if (!manychatSubscriberId) {
      return formatResponse(400, {
        status: "FAILURE",
        message: "manychatSubscriberId is required",
        timestamp: new Date().toISOString(),
      });
    }

    if (!imageURL) {
      return formatResponse(400, {
        status: "FAILURE",
        message: "imageURL is required",
        timestamp: new Date().toISOString(),
      });
    }

    if (!imageCaption) {
      return formatResponse(400, {
        status: "FAILURE",
        message: "imageCaption is required",
        timestamp: new Date().toISOString(),
      });
    }

    // Validate image URL format
    if (!isValidUrl(imageURL)) {
      return formatResponse(400, {
        status: "FAILURE",
        message: "Invalid imageURL format",
        timestamp: new Date().toISOString(),
      });
    }

    console.log("[ReviewMyOutfitController] Processing request:", {
      userId,
      manychatSubscriberId,
      imageCaption,
      imageUrl: imageURL.substring(0, 30) + '...'
    });

    // Create the request object
    const request: ManychatReviewMyOutfitRequest = {
      userId,
      manychatSubscriberId: Number(manychatSubscriberId),
      imageURL,
      imageCaption
    };

    // Call the service method to review the outfit
    const reviewResult = await manychatOutfitService.reviewMyOutfit(request);

    console.log("[ReviewMyOutfitController] Successfully generated outfit review:", {
      userId,
      reviewFeedbackLength: reviewResult.reviewFeedback?.length || 0,
      wardrobeAlternativesCollage: reviewResult.userWardrobeSuggestionsCollage? reviewResult.userWardrobeSuggestionsCollage.substring(0, 30) + '...' : 'N/A',
      marketplaceAlternativesCount: reviewResult.suggestedMarketplaceAlternatives?.length || 0
    });

    return formatResponse(200, {
      status: "SUCCESS",
      message: "Outfit review generated successfully",
      data: reviewResult,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("[ReviewMyOutfitController] Error processing outfit review:", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      body: event.body
    });

    // Return 200 with error status for Manychat compatibility
    return formatResponse(200, {
      status: "FAILURE",
      message: error instanceof Error ? error.message : "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};
