export class ManyChatError extends <PERSON>rror {
  constructor(
    message: string,
    public readonly statusCode: number = 500,
    public readonly errorCode?: string
  ) {
    super(message);
    this.name = "ManyChatError";
  }
}

export const formatResponse = (
  statusCode: number,
  body: any,
  errorCode?: string
) => {
  return {
    statusCode,
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      ...body,
      ...(errorCode && { errorCode }),
    }),
  };
}; 