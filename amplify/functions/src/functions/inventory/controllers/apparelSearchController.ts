import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelCollectionService } from "../../../services/apparelCollectionService";

const apparelService = new ApparelCollectionService();

export const apparelSearchController = {
  async searchApparels(event: APIGatewayProxyEvent) {
    const requestId = event.requestContext.requestId;
    const { query, page, limit, apparelProfile, ...filters } =
      event.queryStringParameters || {};

    if (!query) {
      console.warn(`[ApparelSearchController][${requestId}] Missing search query`);
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "Search query is required",
          code: "MISSING_QUERY",
        }),
      };
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page, 10) : 1;
    const pageSize = limit ? parseInt(limit, 10) : 20;

    console.log(`[ApparelSearchController][${requestId}] Searching apparels:`, {
      query,
      filters,
      apparelProfile,
      page: pageNumber,
      limit: pageSize,
    });

    const searchResult = await apparelService.searchApparels(
      query,
      filters,
      pageNumber,
      pageSize,
      apparelProfile
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Search completed successfully",
        data: searchResult.results,
        metadata: {
          total: searchResult.total,
          page: searchResult.page,
          pageSize: searchResult.pageSize,
          totalPages: searchResult.totalPages,
        },
        query,
        filters,
      }),
    };
  }
}; 