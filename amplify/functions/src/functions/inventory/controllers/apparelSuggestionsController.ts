import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelCollectionService } from "../../../services/apparelCollectionService";

const apparelService = new ApparelCollectionService();

export const apparelSuggestionsController = {
  async getSuggestions(event: APIGatewayProxyEvent) {
    const requestId = event.requestContext.requestId;
    const { q, apparelProfile } = event.queryStringParameters || {};

    if (!q) {
      console.warn(`[ApparelSuggestionsController][${requestId}] Missing suggestion query parameter`);
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "Query parameter 'q' is required",
          code: "MISSING_QUERY",
        }),
      };
    }

    if (q.length < 4) {
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Query too short. Minimum 4 characters required.",
          data: [],
          count: 0,
        }),
      };
    }

    const suggestions = await apparelService.getSuggestions(q, apparelProfile);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Suggestions retrieved successfully",
        data: suggestions,
        count: suggestions.length,
        query: q,
      }),
    };
  }
}; 