import { APIGatewayProxy<PERSON><PERSON><PERSON>, APIGatewayProxyResult } from "aws-lambda";
import { handleRoutes } from "./routes";
import { authenticate } from "./middleware/auth";

// Response formatter
const formatResponse = (
  statusCode: number,
  body: any,
  errorCode?: string
): APIGatewayProxyResult => {
  return {
    statusCode,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: body,
      errorCode: errorCode,
      timestamp: new Date().toISOString(),
    }),
  };
};

// Main handler implementation
export const handler: APIGatewayProxyHandler = async (event, context) => {
  console.log("Event received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
    requestId: context.awsRequestId,
  });

  try {
    // Authenticate the request
   // await authenticate(event);

    // Handle the routes
    const result = await handleRoutes(event, context);
    return formatResponse(result.statusCode, result.body);
  } catch (error) {
    console.error("Error processing request:", {
      error,
      requestId: context.awsRequestId,
      path: event.path,
      method: event.httpMethod,
    });

    if (error instanceof Error) {
      return formatResponse(
        500,
        { message: error.message },
        "INTERNAL_ERROR"
      );
    }

    // Handle unexpected errors
    return formatResponse(
      500,
      { message: "An unexpected error occurred" },
      "INTERNAL_ERROR"
    );
  }
};
