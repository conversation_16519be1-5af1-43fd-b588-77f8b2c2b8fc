import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitService } from "../../../../services/outfitService";
import { OutfitError } from "../../utils/errors";

export const getTodayOutfitsController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const queryParams = event.queryStringParameters || {};
    const { userId, weather } = queryParams;

    if (!userId || !weather) {
      throw new OutfitError(
        "Missing required parameters: userId and weather are required",
        400,
        "MISSING_PARAMETERS"
      );
    }

    const outfitService = new OutfitService();
    const todayOutfitCollection = await outfitService.getTodayOutfits(
      userId,
      weather
    );

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        data: todayOutfitCollection,
        timestamp: new Date().toISOString(),
      }),
    };
  } catch (error) {
    console.error("Error in getTodayOutfitsController:", error);

    if (error instanceof OutfitError) {
      return {
        statusCode: error.statusCode,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({
          data: { message: error.message },
          errorCode: error.errorCode,
          timestamp: new Date().toISOString(),
        }),
      };
    }

    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        data: { message: "An unexpected error occurred" },
        errorCode: "INTERNAL_ERROR",
        timestamp: new Date().toISOString(),
      }),
    };
  }
}; 