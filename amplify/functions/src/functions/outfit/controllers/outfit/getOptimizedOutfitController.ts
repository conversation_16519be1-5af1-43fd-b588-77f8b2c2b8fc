import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitService } from "../../../../services/outfitService";
import { OutfitError } from "../../utils/errors";

export const getOptimizedOutfitController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const queryParams = event.queryStringParameters || {};
    const { userId, vibe, time, location, weather } = queryParams;

    if (!userId || !vibe || !time || !location || !weather) {
      throw new OutfitError(
        "Missing required parameters: userId, vibe, time, location, and weather are required",
        400,
        "MISSING_PARAMETERS"
      );
    }

    const outfitService = new OutfitService();
    const context = {
      eventOutfitVibe: vibe,
      eventTime: time,
      eventLocation: location,
      eventWeather: weather,
    };

    const outfit = await outfitService.getOptimizedOutfit(userId, context);

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        data: outfit,
        timestamp: new Date().toISOString(),
      }),
    };
  } catch (error) {
    console.error("Error in getOptimizedOutfitController:", error);

    if (error instanceof OutfitError) {
      return {
        statusCode: error.statusCode,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({
          data: { message: error.message },
          errorCode: error.errorCode,
          timestamp: new Date().toISOString(),
        }),
      };
    }

    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        data: { message: "An unexpected error occurred" },
        errorCode: "INTERNAL_ERROR",
        timestamp: new Date().toISOString(),
      }),
    };
  }
}; 