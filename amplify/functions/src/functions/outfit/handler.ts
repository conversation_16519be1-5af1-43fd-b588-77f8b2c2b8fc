import { APIGatewayProxyHandler } from "aws-lambda";
import { handleRoutes } from "./routes";

export const handler: APIGatewayProxyHandler = async (event, context) => {
  console.log("Event received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
    requestId: context.awsRequestId,
    RTCCertificate: event.headers?.RTCCertificate,
  });

  return await handleRoutes(event, context);
}; 