/**
 * Simple test file to verify SSO functionality
 * This is for development testing purposes only
 */

import { SSOService } from "../services/ssoService";
import { SSOTokenDao } from "../db/ssoTokenDao";

async function testSSOFunctionality() {
  console.log("=== Testing SSO Functionality ===");
  
  const ssoService = new SSOService();
  const ssoTokenDao = new SSOTokenDao();
  
  try {
    // Test 1: Generate SSO Link
    console.log("\n1. Testing SSO Link Generation...");
    const testUserId = "+1234567890";
    const testManychatId = "12345";
    
    const { ssoLink, token } = await ssoService.generateSSOLink(testUserId, testManychatId);
    console.log("✅ SSO Link generated:", ssoLink);
    console.log("✅ Token:", token);
    
    // Test 2: Validate Token
    console.log("\n2. Testing Token Validation...");
    const validationResult = await ssoService.validateSSOToken(token);
    console.log("✅ Token validation result:", validationResult);
    
    // Test 3: Use Token
    console.log("\n3. Testing Token Usage...");
    const usageResult = await ssoService.useSSOToken(token);
    console.log("✅ Token usage result:", usageResult);
    
    // Test 4: Try to use token again (should fail)
    console.log("\n4. Testing Token Reuse (should fail)...");
    const reuseResult = await ssoService.useSSOToken(token);
    console.log("✅ Token reuse result:", reuseResult);
    
    // Test 5: Test invalid token
    console.log("\n5. Testing Invalid Token...");
    const invalidResult = await ssoService.validateSSOToken("invalid-token");
    console.log("✅ Invalid token result:", invalidResult);
    
    console.log("\n=== All tests completed successfully! ===");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Export for potential use
export { testSSOFunctionality };

// Uncomment the line below to run tests when this file is executed directly
// testSSOFunctionality();
