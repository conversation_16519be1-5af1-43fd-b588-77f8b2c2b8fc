import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  UpdateCommand,
} from "@aws-sdk/lib-dynamodb";
import { SSOTokenData } from "../types/manychat";
import { TABLES } from "./dynamodb";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client, {
  marshallOptions: {
    removeUndefinedValues: true
  }
});

export class SSOTokenDao {
  /**
   * Store SSO token data in DynamoDB
   */
  async createSSOToken(tokenData: SSOTokenData): Promise<SSOTokenData> {
    console.log("[SSOTokenDao][createSSOToken] Creating SSO token:", {
      token: tokenData.token,
      userId: tokenData.userId,
      expiresAt: tokenData.expiresAt
    });

    try {
      await docClient.send(
        new PutCommand({
          TableName: TABLES.OTP_HOLDER,
          Item: tokenData,
          // Ensure token doesn't already exist
          ConditionExpression: "attribute_not_exists(#token)",
          ExpressionAttributeNames: {
            "#token": "token"
          }
        })
      );

      console.log("[SSOTokenDao][createSSOToken] SSO token created successfully:", tokenData.token);
      return tokenData;
    } catch (error) {
      console.error("[SSOTokenDao][createSSOToken] Error creating SSO token:", error);
      throw error;
    }
  }

  /**
   * Retrieve SSO token data from DynamoDB
   */
  async getSSOToken(token: string): Promise<SSOTokenData | null> {
    console.log("[SSOTokenDao][getSSOToken] Retrieving SSO token:", token);

    try {
      const result = await docClient.send(
        new GetCommand({
          TableName: TABLES.OTP_HOLDER,
          Key: { token }
        })
      );

      if (!result.Item) {
        console.log("[SSOTokenDao][getSSOToken] SSO token not found:", token);
        return null;
      }

      console.log("[SSOTokenDao][getSSOToken] SSO token retrieved successfully:", {
        token: result.Item.token,
        userId: result.Item.userId,
        used: result.Item.used,
        expiresAt: result.Item.expiresAt
      });

      return result.Item as SSOTokenData;
    } catch (error) {
      console.error("[SSOTokenDao][getSSOToken] Error retrieving SSO token:", error);
      throw error;
    }
  }

  /**
   * Mark SSO token as used
   */
  async markTokenAsUsed(token: string): Promise<void> {
    console.log("[SSOTokenDao][markTokenAsUsed] Marking token as used:", token);

    try {
      await docClient.send(
        new UpdateCommand({
          TableName: TABLES.OTP_HOLDER,
          Key: { token },
          UpdateExpression: "SET #used = :used",
          ExpressionAttributeNames: {
            "#used": "used"
          },
          ExpressionAttributeValues: {
            ":used": true
          },
          // Ensure token exists before updating
          ConditionExpression: "attribute_exists(#token)",
          ExpressionAttributeNames: {
            "#token": "token",
            "#used": "used"
          }
        })
      );

      console.log("[SSOTokenDao][markTokenAsUsed] Token marked as used successfully:", token);
    } catch (error) {
      console.error("[SSOTokenDao][markTokenAsUsed] Error marking token as used:", error);
      throw error;
    }
  }

  /**
   * Check if token is valid (exists, not used, not expired)
   */
  async isTokenValid(token: string): Promise<{ valid: boolean; tokenData?: SSOTokenData; reason?: string }> {
    console.log("[SSOTokenDao][isTokenValid] Validating token:", token);

    try {
      const tokenData = await this.getSSOToken(token);
      
      if (!tokenData) {
        return { valid: false, reason: "Token not found" };
      }

      if (tokenData.used) {
        return { valid: false, tokenData, reason: "Token already used" };
      }

      const currentTime = Math.floor(Date.now() / 1000);
      if (tokenData.expiresAt < currentTime) {
        return { valid: false, tokenData, reason: "Token expired" };
      }

      console.log("[SSOTokenDao][isTokenValid] Token is valid:", token);
      return { valid: true, tokenData };
    } catch (error) {
      console.error("[SSOTokenDao][isTokenValid] Error validating token:", error);
      return { valid: false, reason: "Error validating token" };
    }
  }
}
