import { AIUtil } from '../../utils/aiUtil';
import { OpenWeatherService } from '../../ai/integrations/OpenWeatherIntegration';

export class ManychatUtilService {
  private aiUtil: AIUtil;
  private openWeatherService: OpenWeatherService;

  constructor() {
    this.aiUtil = new AIUtil();
    this.openWeatherService = new OpenWeatherService();
  }

  /**
   * Get a valid city from user input, or return ERROR if not found
   * @param req { manychatSubscriberId: string, userInput: string }
   * @returns { response: string, message: string }
   */
  async getValidCityFromText(req: { manychatSubscriberId: string, userInput: string }): Promise<{ response: string, message: string }> {
    console.log('[ManychatUtilService][getValidCityFromText] Start', { req });
    try {
      const { userInput } = req;
      console.log('[ManychatUtilService][getValidCityFromText] User input:', userInput);
      // Step 1: Extract city candidates from AIUtil
      const { cities, message: aiMessage } = await this.aiUtil.getCityCandidatesFromText(userInput);
      console.log('[ManychatUtilService][getValidCityFromText] City candidates from AI:', { cities, aiMessage });
      if (!cities || cities.length === 0) {
        console.log('[ManychatUtilService][getValidCityFromText] No city candidates found. Returning ERROR.');
        return { response: 'ERROR', message: aiMessage || "Sorry, I couldn't find any valid city in your input." };
      }
      // Step 2: Check each city with OpenWeather
      for (const city of cities.slice(0, 3)) {
        console.log(`[ManychatUtilService][getValidCityFromText] Checking city with OpenWeather: ${city}`);
        const result = await this.openWeatherService.isValidCity(city);
        console.log(`[ManychatUtilService][getValidCityFromText] OpenWeather result for '${city}':`, result);
        if (result.valid) {
          const cityString = result.city;
          console.log('[ManychatUtilService][getValidCityFromText] Found valid city:', cityString);
          return { response: cityString, message: `Found valid city: ${cityString}` };
        }
      }
      // If none are valid
      console.log('[ManychatUtilService][getValidCityFromText] No valid city found after OpenWeather checks. Returning ERROR.');
      return { response: 'ERROR', message: "Sorry, I couldn't find a valid city that matches your input." };
    } catch (error) {
      console.error('[ManychatUtilService][getValidCityFromText] Error:', error);
      return { response: 'ERROR', message: "Sorry, something went wrong while processing your request." };
    } finally {
      console.log('[ManychatUtilService][getValidCityFromText] End');
    }
  }
} 