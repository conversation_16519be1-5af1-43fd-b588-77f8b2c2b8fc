import { TypeChatService } from "../ai/integrations/TypeChatIntegration";
import { OutfitService } from "../services/outfitService";
import { ApparelCollectionService } from "../services/apparelCollectionService";
import { StyleProfileService } from "../services/styleProfileService";
import { ManychatStyleProfile, StyleProfileStore } from "../types/style.types";
import { ManychatSituationalContext } from "../types/manychat";
import {
  SituationalContext,
  OutfitCurationResponse,
  OutfitStatus,
  OutfitStore,
  GenericApparelItem,
  OutfitError,
} from "../types/outfits.types";
import {
  ApparelMetaData,
  ApparelSource,
  ApparelVibe,
  MaleApparelType,
  FemaleApparelType,
  ApparelCategory,
  Apparel
} from "../types/apparel.types";
import { ApparelService } from "../services/apparelService";
import { AIService } from "../ai/integrations/AIIntegration";
import { outfitCurationWithExistingApparelsPrompt } from '../ai/prompts/outfitCurationWithExistingApparelsPrompt';
import { manychatOutfitCurationPrompt } from "../ai/prompts/manychatOutfitCurationPrompt";
import { manychatOutfitCurationWithExistingApparelsPrompt } from "../ai/prompts/manychatOutfitCurationWithExistingApparelsPrompt";

export class OutfitCurationUtil {
  private aiService: AIService;
  private typeChatService: TypeChatService;
  private outfitService: OutfitService | null;
  private apparelService: ApparelService;
  private apparelCollectionService: ApparelCollectionService;
  private styleProfileService: StyleProfileService;

  constructor() {
    this.aiService = new AIService();
    this.typeChatService = new TypeChatService();
    this.outfitService = null;
    this.apparelService = new ApparelService();
    this.apparelCollectionService = new ApparelCollectionService();
    this.styleProfileService = new StyleProfileService();
  }

  public setOutfitService(outfitService: OutfitService) {
    this.outfitService = outfitService;
  }

  private async generateOutfitFromExistingApparels(
    styleProfileInfo: StyleProfileStore,
    context: SituationalContext,
    apparels: ApparelMetaData[]
  ): Promise<OutfitStore> {
    if (!this.outfitService) {
      throw new Error("OutfitService not initialized");
    }

    console.log(`Generating outfit...`);
    const userPrompt = {
      styleProfileInfo: styleProfileInfo,
      apparels: apparels,
      context: context,
    };
    console.log(`User Prompt: ${userPrompt}`);

    const aiResponse = await this.aiService.getGeminiTextAnalysis(
      JSON.stringify(userPrompt),
      outfitCurationWithExistingApparelsPrompt
    );

    console.log(`AI Response: ${aiResponse}`);

    const parsedResponse =
      await this.typeChatService.parseOutfitCurationResponse(aiResponse);

    console.log(`Parsed Response: ${parsedResponse}`);

    console.log(parsedResponse);

    const validApparelItems = await this.saveNewApparels(parsedResponse, styleProfileInfo.userId);

    console.log("Valid Apparel Item : ", validApparelItems);

    // from all apparels create a set of all the apparelvibe and then put that array into tags
    const tags = Array.from(
      new Set(
        validApparelItems
          .map((item) => item.vibe)
          .filter(
            (vibe): vibe is ApparelVibe => vibe !== null && vibe !== undefined
          )
      )
    );

    const outfitName = parsedResponse.outfitName;
    const outfit = await this.outfitService.createOutfit({
      userId: styleProfileInfo.userId,
      outfitName: outfitName,
      apparelPointers: validApparelItems.map((item) => item.apparelId),
      status: OutfitStatus.PRIMARY,
      situationContext: context,
      tags: tags,
    });
    return outfit;
  }

  //TODO : This method is not needed since why will we save NEW apparels that AI selected during outfit curation to User's Wardrobe!
  private async saveNewApparels(parsedResponse: OutfitCurationResponse, userId: string) {
    const processedApparelItems = await Promise.all(
      parsedResponse.apparelItems.map(async (apparelItem) => {
        // Only process items marked as new
        if (apparelItem.apparelId.toLowerCase() !== "new") {
          return apparelItem;
        }

        const searchQuery = this.createSimpleSearchQuery(apparelItem);
        console.log(`[OutfitCurationUtil] Searching for: ${searchQuery}`);
        const searchResults =
          await this.apparelCollectionService.searchApparels(searchQuery);

        if (searchResults.results.length > 0) {
          try {
            // Create a simplified metadata object with just the essential fields
            const apparelMetaData: ApparelMetaData =
              apparelItem.apparelProfile === "MALE"
                ? {
                    apparelMediaUrl: searchResults.results[0].imageUrl,
                    apparelCategory:
                      apparelItem.apparelCategory as ApparelCategory,
                    apparelProfile: "MALE",
                    apparelType: apparelItem.apparelType as MaleApparelType,
                    vibe: apparelItem.vibe || undefined,
                  }
                : {
                    apparelMediaUrl: searchResults.results[0].imageUrl,
                    apparelCategory:
                      apparelItem.apparelCategory as ApparelCategory,
                    apparelProfile: "FEMALE",
                    apparelType: apparelItem.apparelType as FemaleApparelType,
                    vibe: apparelItem.vibe || undefined,
                  };

            // Save to database with SYSTEM source
            const savedApparel = await this.apparelService.createApparel(
              ApparelSource.SYSTEM,
              apparelMetaData,
              userId,
              "MONOVA"
            );

            // Update the apparel item with the new ID
            return {
              ...apparelItem,
              apparelId: savedApparel.apparelId,
            };
          } catch (error) {
            console.error(
              "[OutfitCurationUtil] Error saving new apparel:",
              error
            );
            return null;
          }
        }
        return null; // Return null for items with no search results
      })
    );

    // Filter out null values and return the processed items
    return processedApparelItems.filter(
      (item): item is GenericApparelItem => item !== null
    );
  }

  private createSimpleSearchQuery(apparelDetails: GenericApparelItem): string {
    // Define the attributes to include in order of importance
    const attributes = [
      "apparelType",
      "colour",
      "pattern",
      "fit",
      "fabric",
      "length",
      "sleeves",
      "shape",
    ] as const;

    // Filter out undefined/null/empty values and join with spaces
    const searchTerms = attributes
      .map((attr) => apparelDetails[attr])
      .filter((value) => value && typeof value === "string")
      .join(" ");

    console.log(`[OutfitCurationUtil] Generated search query: ${searchTerms}`);
    return searchTerms;
  }

  async processOutfitCurationFromExistingApparels(
    userId: string,
    caseCotexts: SituationalContext[],
    apparels: ApparelMetaData[]
  ): Promise<OutfitStore[]> {
    console.log(`Processing outfit curation...`, caseCotexts);
    const styleProfile = await this.styleProfileService.getProfile(userId);
    console.log(`Style Profile: ${styleProfile}`);
    console.log(`Apparels: ${apparels}`);

    // Call generateOutfit for each caseContext in parallel
    const outfits = await Promise.all(
      caseCotexts.map(async (context) => {
        try {
          return await this.generateOutfitFromExistingApparels(styleProfile, context, apparels);
        } catch (error) {
          console.error(
            `Error generating outfit for context ${context}:`,
            error
          );
          return null; // Return null for outfits that fail to generate
        }
      })
    );

    // Filter out null values (failed outfits)
    const validOutfits = outfits.filter(
      (outfit): outfit is OutfitStore => outfit !== null
    );
    console.log(`Valid Outfits: ${validOutfits}`);
    return validOutfits;
  }

  async processOutfitCurationGenAIManychat(
    styleProfile: ManychatStyleProfile,
    context: ManychatSituationalContext
  ): Promise<string> {
    console.log(`Processing outfit curation with GenAI...`);

    // Create user prompt with style profile and context
    const userPrompt = {
      StyleProfileInfo: styleProfile,
      Context: context,
    };

    // Add iteration parameters if both are present
    if (context.suggestedOutfit && context.userOverride) {
      console.log(`Processing outfit iteration/refinement request`);
      console.log(`Previous outfit provided for iteration: ${context.suggestedOutfit.substring(0, 100)}...`);
      console.log(`User override provided: ${context.userOverride}`);
    }

    console.log(`User Prompt: ${JSON.stringify(userPrompt)}`);

    // Call Gemini AI with the prompt
    const aiResponse = await this.aiService.getGeminiTextAnalysis(
      JSON.stringify(userPrompt),
      manychatOutfitCurationPrompt
    );

    console.log(`AI Response: ${aiResponse}`);

    // Return the raw response string instead of parsing it
    return aiResponse;
  }

  /**
   * Process outfit curation from existing apparels specifically for Manychat
   * This method uses a simplified prompt that doesn't require time/location/weather information
   * @param userId The user ID
   * @param context The Manychat situational context (occasion and style preference only)
   * @param apparels The user's existing apparels
   * @returns An object with outfit information (not saved to database)
   */
  async processOutfitCurationFromExistingApparelsManychat(
    userId: string,
    context: ManychatSituationalContext,
    apparels: ApparelMetaData[]
  ): Promise<{
    outfitName: string;
    apparelPointers: string[];
    apparelItems: GenericApparelItem[];
  }> {
    console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Processing outfit curation for Manychat...`, {
      userId,
      context: JSON.stringify(context),
      apparelsCount: apparels.length
    });
    console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Apparels:`, apparels);

    const styleProfile = await this.styleProfileService.getProfile(userId);
    console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Retrieved style profile for user ${userId}`);

    try {
      // Create user prompt with style profile, context, and apparels
      const userPrompt = {
        styleProfileInfo: styleProfile,
        context: context,
        apparels: apparels,
      };

      console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Sending request to AI service with user prompt:`, userPrompt);

      // Call Gemini AI with the Manychat-specific prompt
      const aiResponse = await this.aiService.getGeminiTextAnalysis(
        JSON.stringify(userPrompt),
        manychatOutfitCurationWithExistingApparelsPrompt
      );

      console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Received AI response`, aiResponse);

      // Parse the response
      const parsedResponse = await this.typeChatService.parseOutfitCurationResponse(aiResponse);
      console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Parsed response:`, parsedResponse);

      // Filter out any "NEW" apparels - we only want to use existing apparels
      const validApparelItems = parsedResponse.apparelItems.filter(
        item => item.apparelId.toLowerCase() !== "new"
      );

      console.log(`[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Valid apparel items:`, validApparelItems);

      // Return the outfit information without saving to database
      return {
        outfitName: parsedResponse.outfitName,
        apparelPointers: validApparelItems.map(item => item.apparelId),
        apparelItems: validApparelItems
      };
    } catch (error) {
      console.error(
        `[OutfitCurationUtil][processOutfitCurationFromExistingApparelsManychat] Error generating outfit:`,
        error
      );
      throw error;
    }
  }

  /**
   * Validates if the provided apparels meet the minimum requirements for outfit curation
   * @param apparels Array of apparels to validate
   * @throws OutfitError if the apparels don't meet the minimum requirements
   */
  public validateEligibilityForOutfitCuration(apparels: Apparel[]): void {
    if (apparels.length < 6) {
      console.error("[OutfitCurationUtil][validateEligibilityForOutfitCuration] Insufficient apparels:", apparels.length);
      throw new OutfitError(
        "Minimum 6 apparels required to create outfit combinations",
        400,
        "INSUFFICIENT_APPARELS"
      );
    }

    // Check minimum required items by category
    const categorizedCounts = apparels.reduce(
      (acc, apparel) => {
        if (!apparel) return acc;
        switch (apparel.apparelCategory.toUpperCase()) {
          case "TOPWEAR":
            acc.tops++;
            break;
          case "BOTTOMWEAR":
            acc.bottoms++;
            break;
          case "FOOTWEAR":
            acc.footwear++;
            break;
        }
        return acc;
      },
      { tops: 0, bottoms: 0, footwear: 0 }
    );
    console.log("[OutfitCurationUtil][validateEligibilityForOutfitCuration] Category counts:", categorizedCounts);

    if (categorizedCounts.tops < 2 ||
      categorizedCounts.bottoms < 2 ||
      categorizedCounts.footwear < 2) {
      console.error("[OutfitCurationUtil][validateEligibilityForOutfitCuration] Insufficient category items:", categorizedCounts);
      throw new OutfitError(
        "Minimum 2 tops, 2 bottoms, and 2 footwear items required for outfit combinations",
        400,
        "INSUFFICIENT_CATEGORY_ITEMS"
      );
    }
  }
}
